<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三角洲地图扫描监控系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            height: calc(100vh - 40px);
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .status-bar {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            margin: 5px 0;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-detecting {
            background: #ffc107;
            animation: pulse 1.5s infinite;
        }
        
        .status-idle {
            background: #28a745;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        

        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .main-content {
            display: flex;
            flex: 1;
            overflow: hidden;
        }

        .left-panel {
            width: 400px;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .right-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .panel-header {
            padding: 20px;
            background: white;
            border-bottom: 1px solid #e9ecef;
            font-weight: bold;
            color: #333;
        }

        .server-list {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }

        .server-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .server-item:hover {
            background: #f0f8ff;
            border-color: #667eea;
            transform: translateX(5px);
        }

        .server-item.selected {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }

        .server-item.selected .message-count {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .server-address {
            font-weight: bold;
            font-size: 1.1em;
            margin-bottom: 8px;
        }

        .server-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9em;
        }

        .message-count {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 4px 10px;
            border-radius: 12px;
            font-weight: bold;
            font-size: 0.8em;
        }

        .preview-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .preview-header {
            padding: 15px 20px;
            background: white;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .preview-url {
            font-family: monospace;
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #e9ecef;
            color: #333;
        }

        .preview-frame {
            flex: 1;
            border: none;
            background: white;
        }

        .no-selection {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
            color: #666;
            font-size: 1.2em;
        }

        .no-selection .icon {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.3;
        }
        
        .detection-time {
            color: #666;
            font-size: 0.8em;
        }
        
        .history-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .history-table th,
        .history-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .history-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
        }
        
        .history-table tr:hover {
            background: #f8f9fa;
        }
        
        .no-data {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 40px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .auto-refresh {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 14px;
        }
        
        @media (max-width: 768px) {
            .status-bar {
                flex-direction: column;
                align-items: flex-start;
            }

            .main-content {
                flex-direction: column;
            }

            .left-panel {
                width: 100%;
                height: 300px;
            }

            .btn {
                margin: 5px;
                padding: 10px 20px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="auto-refresh" id="autoRefresh">自动刷新: 5秒</div>
    
    <div class="container">
        <div class="header">
            <h1>🗺️ 三角洲地图扫描监控系统</h1>
            <p>实时监控WebSocket服务器活跃状态 | 每分钟自动检测一次</p>
        </div>
        
        <div class="status-bar">
            <div class="status-item">
                <div class="status-indicator" id="statusIndicator"></div>
                <span id="statusText">正在加载...</span>
            </div>
            <div class="status-item">
                <strong>当前选中: </strong>
                <span id="selectedServer">无</span>
            </div>
            <div class="status-item">
                <strong>最后检测: </strong>
                <span id="lastDetection">-</span>
            </div>
        </div>
        

        
        <div class="main-content">
            <!-- 左侧服务器列表 -->
            <div class="left-panel">
                <div class="panel-header">
                    📊 活跃服务器列表 (<span id="activeCount">0</span>)
                </div>
                <div class="server-list" id="serverList">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>正在加载服务器列表...</p>
                    </div>
                </div>
            </div>

            <!-- 右侧预览区域 -->
            <div class="right-panel">
                <div class="preview-container" id="previewContainer">
                    <div class="no-selection">
                        <div class="icon">🖥️</div>
                        <p>请从左侧选择一个服务器进行预览</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let refreshInterval;
        let countdownInterval;
        let countdown = 5;
        let selectedServer = null;

        // 页面加载完成后开始自动刷新
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
            startAutoRefresh();
        });

        function startAutoRefresh() {
            // 每5秒刷新一次
            refreshInterval = setInterval(refreshData, 5000);
            
            // 倒计时显示
            countdownInterval = setInterval(function() {
                countdown--;
                if (countdown <= 0) {
                    countdown = 5;
                }
                document.getElementById('autoRefresh').textContent = `自动刷新: ${countdown}秒`;
            }, 1000);
        }

        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
            if (countdownInterval) {
                clearInterval(countdownInterval);
            }
        }

        async function refreshData() {
            try {
                // 重置倒计时
                countdown = 5;
                
                // 获取状态
                const statusResponse = await fetch('/api/status');
                const status = await statusResponse.json();
                updateStatus(status);
                
                // 获取活跃服务器
                const serversResponse = await fetch('/api/active_servers');
                const servers = await serversResponse.json();
                updateServerList(servers);
                
            } catch (error) {
                console.error('刷新数据失败:', error);
            }
        }

        function updateStatus(status) {
            const indicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');
            const lastDetection = document.getElementById('lastDetection');

            if (status.is_detecting) {
                indicator.className = 'status-indicator status-detecting';
                statusText.textContent = '正在检测中...';
            } else {
                indicator.className = 'status-indicator status-idle';
                statusText.textContent = '等待中';
            }

            lastDetection.textContent = status.last_detection || '暂无';
        }

        function updateServerList(servers) {
            const container = document.getElementById('serverList');
            const activeCount = document.getElementById('activeCount');

            activeCount.textContent = servers ? servers.length : 0;

            if (!servers || servers.length === 0) {
                container.innerHTML = '<div class="no-data">暂无活跃服务器</div>';
                return;
            }

            const html = servers.map(server => `
                <div class="server-item" onclick="selectServer('${server.server}', ${server.message_count})"
                     data-server="${server.server}">
                    <div class="server-address">${server.server}</div>
                    <div class="server-stats">
                        <div class="message-count">${server.message_count} 条消息</div>
                        <div class="detection-time">${server.detection_time}</div>
                    </div>
                </div>
            `).join('');

            container.innerHTML = html;

            // 如果之前有选中的服务器，保持选中状态
            if (selectedServer) {
                const serverItem = container.querySelector(`[data-server="${selectedServer}"]`);
                if (serverItem) {
                    serverItem.classList.add('selected');
                }
            }
        }

        function selectServer(serverAddress, messageCount) {
            // 移除之前的选中状态
            const previousSelected = document.querySelector('.server-item.selected');
            if (previousSelected) {
                previousSelected.classList.remove('selected');
            }

            // 添加新的选中状态
            const serverItem = document.querySelector(`[data-server="${serverAddress}"]`);
            if (serverItem) {
                serverItem.classList.add('selected');
            }

            selectedServer = serverAddress;

            // 更新状态栏显示
            const selectedServerSpan = document.getElementById('selectedServer');
            selectedServerSpan.textContent = serverAddress;

            // 显示预览
            showServerPreview(serverAddress);
        }

        function showServerPreview(serverAddress) {
            const previewContainer = document.getElementById('previewContainer');

            // 使用代理URL来避免跨域问题并自动注入WebSocket重写脚本
            const proxyUrl = `/proxy/${serverAddress}`;

            // 构建原始URL用于显示
            let originalUrl;
            if (serverAddress.includes(':')) {
                const [host, port] = serverAddress.split(':');
                originalUrl = `http://${host}:${port}`;
            } else {
                originalUrl = `http://${serverAddress}:9000`;
            }

            const html = `
                <div class="preview-header">
                    <div>
                        <strong>预览服务器:</strong> ${serverAddress}
                    </div>
                    <div class="preview-url">
                        <span style="color: #666; font-size: 0.9em;">通过代理访问:</span> ${originalUrl}
                    </div>
                </div>
                <iframe class="preview-frame" src="${proxyUrl}"
                        onload="handleFrameLoad(this)"
                        onerror="handleFrameError('${serverAddress}')">
                </iframe>
            `;

            previewContainer.innerHTML = html;
        }

        function handleFrameLoad(iframe) {
            console.log('预览加载成功');
            console.log('WebSocket重写脚本已通过代理服务器自动注入');

            // 可选：尝试检查脚本是否成功注入（如果同源的话）
            try {
                const iframeWindow = iframe.contentWindow;
                if (iframeWindow && iframeWindow.WebSocket) {
                    console.log('✓ WebSocket对象可访问，脚本应该已成功注入');
                }
            } catch (error) {
                // 跨域时会出现这个错误，这是正常的
                console.log('iframe内容受跨域保护，但WebSocket重写脚本已通过服务器端注入');
            }
        }

        function handleFrameError(serverAddress) {
            const previewContainer = document.getElementById('previewContainer');
            previewContainer.innerHTML = `
                <div class="no-selection">
                    <div class="icon">⚠️</div>
                    <p>无法加载服务器预览: ${serverAddress}</p>
                    <p style="font-size: 0.9em; color: #999; margin-top: 10px;">
                        服务器可能不提供HTTP服务或存在跨域限制
                    </p>
                </div>
            `;
        }



        // 页面卸载时停止自动刷新
        window.addEventListener('beforeunload', function() {
            stopAutoRefresh();
        });
    </script>
</body>
</html>
