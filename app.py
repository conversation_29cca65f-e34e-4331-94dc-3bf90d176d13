from flask import Flask, render_template, jsonify, request, Response
import threading
import websocket
import time
import json
from datetime import datetime
import os
import queue
from collections import defaultdict
import requests
from urllib.parse import urljoin

app = Flask(__name__)

# 全局变量
server_statuses = {}  # 存储服务器状态
active_servers = []  # 存储活跃服务器列表
detection_results = []  # 存储检测结果
is_detecting = False  # 检测状态标志
stop_event = threading.Event()  # 停止事件
detection_thread = None  # 检测线程

class ServerDetector:
    def __init__(self):
        self.message_counts = defaultdict(int)  # 消息计数器
        self.detection_start_time = None
        
    def load_servers(self):
        """从server.txt加载服务器列表"""
        servers = []
        
        # 优先使用活跃的服务器文件
        for filename in ["active_servers.txt", "websocket_working_servers.txt", 
                        "available_servers.txt", "server.txt"]:
            if os.path.exists(filename):
                with open(filename, "r", encoding="utf-8") as f:
                    servers = [line.strip() for line in f.readlines() if line.strip()]
                print(f"从{filename}中读取到 {len(servers)} 个服务器")
                break
        
        return servers
    
    def test_server_connection(self, server, timeout=10):
        """测试单个服务器连接并计算消息数"""
        message_count = 0
        connected = False
        
        def on_open(ws):
            nonlocal connected
            connected = True
            print(f"成功连接到 {server}")
        
        def on_message(ws, message):
            nonlocal message_count
            message_count += 1
        
        def on_error(ws, error):
            print(f"连接 {server} 时出错: {error}")
        
        def on_close(ws, close_status_code, close_msg):
            print(f"连接到 {server} 已关闭")
        
        try:
            # 尝试WS连接
            server_str = str(server).strip()
            if ":" in server_str:
                host = server_str.split(":")[0]
                port = server_str.split(":")[1]
            else:
                host = server_str
                port = "9000"
            
            ws_url = f"ws://{host}:{port}"
            
            ws = websocket.WebSocketApp(
                ws_url,
                on_open=on_open,
                on_message=on_message,
                on_error=on_error,
                on_close=on_close
            )
            
            # 运行连接，限制时间
            ws.run_forever(ping_interval=30, ping_timeout=10)
            
        except Exception as e:
            # 如果WS失败，尝试WSS
            try:
                wss_url = f"wss://{server_str}"
                ws = websocket.WebSocketApp(
                    wss_url,
                    on_open=on_open,
                    on_message=on_message,
                    on_error=on_error,
                    on_close=on_close
                )
                ws.run_forever(sslopt={"cert_reqs": 0}, ping_interval=30, ping_timeout=10)
            except Exception as e2:
                print(f"连接 {server} 失败: {e2}")
        
        return message_count, connected
    
    def detect_active_servers(self, detection_time=10):
        """检测活跃服务器（检测指定时间）"""
        global is_detecting, active_servers, detection_results
        
        is_detecting = True
        self.detection_start_time = datetime.now()
        servers = self.load_servers()
        
        if not servers:
            is_detecting = False
            return []
        
        print(f"开始检测 {len(servers)} 个服务器，检测时间: {detection_time}秒")
        
        # 重置消息计数器
        self.message_counts.clear()
        threads = []
        
        def test_server_with_timeout(server):
            """带超时的服务器测试"""
            start_time = time.time()
            message_count = 0
            
            def on_message_wrapper(ws, message):
                nonlocal message_count
                # 只在检测时间内计数
                if time.time() - start_time < detection_time:
                    message_count += 1
                    self.message_counts[server] = message_count
            
            def on_open_wrapper(ws):
                print(f"连接到 {server}")
            
            def on_error_wrapper(ws, error):
                pass  # 静默处理错误
            
            def on_close_wrapper(ws, close_status_code, close_msg):
                pass  # 静默处理关闭
            
            try:
                server_str = str(server).strip()
                if ":" in server_str:
                    host = server_str.split(":")[0]
                    port = server_str.split(":")[1]
                else:
                    host = server_str
                    port = "9000"
                
                ws_url = f"ws://{host}:{port}"
                
                ws = websocket.WebSocketApp(
                    ws_url,
                    on_open=on_open_wrapper,
                    on_message=on_message_wrapper,
                    on_error=on_error_wrapper,
                    on_close=on_close_wrapper
                )
                
                # 创建一个定时器来关闭连接
                def close_connection():
                    time.sleep(detection_time)
                    try:
                        ws.close()
                    except:
                        pass
                
                timer = threading.Thread(target=close_connection, daemon=True)
                timer.start()
                
                # 运行连接
                ws.run_forever(ping_interval=30, ping_timeout=5)
                
            except Exception as e:
                # 尝试WSS连接
                try:
                    wss_url = f"wss://{server_str}"
                    ws = websocket.WebSocketApp(
                        wss_url,
                        on_open=on_open_wrapper,
                        on_message=on_message_wrapper,
                        on_error=on_error_wrapper,
                        on_close=on_close_wrapper
                    )
                    
                    def close_connection():
                        time.sleep(detection_time)
                        try:
                            ws.close()
                        except:
                            pass
                    
                    timer = threading.Thread(target=close_connection, daemon=True)
                    timer.start()
                    
                    ws.run_forever(sslopt={"cert_reqs": 0}, ping_interval=30, ping_timeout=5)
                except:
                    pass
        
        # 为每个服务器创建线程
        for server in servers:
            if stop_event.is_set():
                break
            thread = threading.Thread(target=test_server_with_timeout, args=(server,), daemon=True)
            threads.append(thread)
            thread.start()
        
        # 等待检测完成
        time.sleep(detection_time + 2)  # 多等待2秒确保所有连接关闭
        
        # 筛选出消息数超过10的服务器
        active_servers = []
        for server, count in self.message_counts.items():
            if count > 10:
                active_servers.append({
                    'server': server,
                    'message_count': count,
                    'detection_time': self.detection_start_time.strftime("%Y-%m-%d %H:%M:%S")
                })
        
        # 按消息数排序
        active_servers.sort(key=lambda x: x['message_count'], reverse=True)
        
        # 保存检测结果
        detection_results.append({
            'timestamp': self.detection_start_time.strftime("%Y-%m-%d %H:%M:%S"),
            'total_servers': len(servers),
            'active_servers': len(active_servers),
            'servers': active_servers.copy()
        })
        
        # 只保留最近10次检测结果
        if len(detection_results) > 10:
            detection_results.pop(0)
        
        print(f"检测完成，发现 {len(active_servers)} 个活跃服务器（消息数>10）")
        is_detecting = False
        
        return active_servers

def auto_detection_loop():
    """自动检测循环：每1分钟检测一次"""
    detector = ServerDetector()
    
    while not stop_event.is_set():
        try:
            print("开始自动检测...")
            detector.detect_active_servers(detection_time=10)
            print("检测完成，等待下次检测...")
            
            # 等待60秒（1分钟）
            for _ in range(60):
                if stop_event.is_set():
                    break
                time.sleep(1)
                
        except Exception as e:
            print(f"自动检测出错: {e}")
            time.sleep(10)  # 出错后等待10秒再重试

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/test-websocket')
def test_websocket():
    """测试WebSocket重写功能的页面"""
    return '''
<!DOCTYPE html>
<html>
<head>
    <title>WebSocket重写测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .log { background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; max-height: 200px; overflow-y: auto; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 WebSocket重写功能测试</h1>
        <p>这个页面用于测试WebSocket协议重写功能（wss:// → ws://）</p>

        <div class="test-section">
            <h3>测试1: 直接创建WSS连接</h3>
            <button onclick="testDirectWSS()">测试 wss://echo.websocket.org</button>
            <div id="log1" class="log"></div>
        </div>

        <div class="test-section">
            <h3>测试2: 创建WS连接（对照组）</h3>
            <button onclick="testDirectWS()">测试 ws://echo.websocket.org</button>
            <div id="log2" class="log"></div>
        </div>

        <div class="test-section">
            <h3>测试3: 检查WebSocket对象</h3>
            <button onclick="checkWebSocketObject()">检查WebSocket对象</button>
            <div id="log3" class="log"></div>
        </div>
    </div>

    <script>
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : (type === 'success' ? 'success' : '');
            element.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\\n`;
            element.scrollTop = element.scrollHeight;
        }

        function testDirectWSS() {
            const log1 = document.getElementById('log1');
            log1.innerHTML = '';
            log('log1', '开始测试 wss:// 连接...');

            try {
                const ws = new WebSocket('wss://echo.websocket.org');
                log('log1', '✓ WebSocket对象创建成功');
                log('log1', `实际URL: ${ws.url}`, 'success');

                ws.onopen = function() {
                    log('log1', '✓ 连接已建立', 'success');
                    ws.send('Hello from WSS test!');
                };

                ws.onmessage = function(event) {
                    log('log1', `✓ 收到消息: ${event.data}`, 'success');
                    ws.close();
                };

                ws.onclose = function() {
                    log('log1', '连接已关闭');
                };

                ws.onerror = function(error) {
                    log('log1', `✗ 连接错误: ${error}`, 'error');
                };

            } catch (error) {
                log('log1', `✗ 创建WebSocket失败: ${error}`, 'error');
            }
        }

        function testDirectWS() {
            const log2 = document.getElementById('log2');
            log2.innerHTML = '';
            log('log2', '开始测试 ws:// 连接...');

            try {
                const ws = new WebSocket('ws://echo.websocket.org');
                log('log2', '✓ WebSocket对象创建成功');
                log('log2', `实际URL: ${ws.url}`, 'success');

                ws.onopen = function() {
                    log('log2', '✓ 连接已建立', 'success');
                    ws.send('Hello from WS test!');
                };

                ws.onmessage = function(event) {
                    log('log2', `✓ 收到消息: ${event.data}`, 'success');
                    ws.close();
                };

                ws.onclose = function() {
                    log('log2', '连接已关闭');
                };

                ws.onerror = function(error) {
                    log('log2', `✗ 连接错误: ${error}`, 'error');
                };

            } catch (error) {
                log('log2', `✗ 创建WebSocket失败: ${error}`, 'error');
            }
        }

        function checkWebSocketObject() {
            const log3 = document.getElementById('log3');
            log3.innerHTML = '';
            log('log3', '检查WebSocket对象...');

            log('log3', `WebSocket构造函数: ${typeof WebSocket}`);
            log('log3', `WebSocket.prototype: ${typeof WebSocket.prototype}`);
            log('log3', `WebSocket.CONNECTING: ${WebSocket.CONNECTING}`);
            log('log3', `WebSocket.OPEN: ${WebSocket.OPEN}`);
            log('log3', `WebSocket.CLOSING: ${WebSocket.CLOSING}`);
            log('log3', `WebSocket.CLOSED: ${WebSocket.CLOSED}`);

            // 检查是否被重写
            if (window.OriginalWebSocket) {
                log('log3', '✓ 检测到WebSocket已被重写', 'success');
            } else {
                log('log3', '? 未检测到重写标记（可能是正常的）');
            }
        }
    </script>
</body>
</html>
    '''

@app.route('/api/status')
def get_status():
    """获取当前状态"""
    return jsonify({
        'is_detecting': is_detecting,
        'active_servers_count': len(active_servers),
        'last_detection': detection_results[-1]['timestamp'] if detection_results else None
    })

@app.route('/api/active_servers')
def get_active_servers():
    """获取活跃服务器列表"""
    return jsonify(active_servers)

@app.route('/api/detection_history')
def get_detection_history():
    """获取检测历史"""
    return jsonify(detection_results)

@app.route('/api/start_detection')
def start_detection():
    """手动开始检测"""
    global detection_thread

    if not is_detecting:
        detector = ServerDetector()
        detection_thread = threading.Thread(
            target=detector.detect_active_servers,
            args=(10,),
            daemon=True
        )
        detection_thread.start()
        return jsonify({'status': 'started'})
    else:
        return jsonify({'status': 'already_running'})

@app.route('/proxy/<path:server_address>')
def proxy_server(server_address):
    """代理服务器请求，避免跨域问题"""
    try:
        # 构建目标URL
        if ':' in server_address:
            target_url = f"http://{server_address}"
        else:
            target_url = f"http://{server_address}:9000"

        # 获取查询参数
        query_string = request.query_string.decode('utf-8')
        if query_string:
            target_url += f"?{query_string}"

        # 转发请求
        resp = requests.get(target_url, timeout=10, allow_redirects=True)

        # 创建响应
        response = Response(resp.content, status=resp.status_code)

        # 复制重要的响应头，但排除可能导致问题的头
        excluded_headers = ['content-encoding', 'content-length', 'transfer-encoding', 'connection']
        for key, value in resp.headers.items():
            if key.lower() not in excluded_headers:
                response.headers[key] = value

        # 添加CORS头以避免跨域问题
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = '*'

        # 如果是HTML内容，注入WebSocket重写脚本
        if 'text/html' in resp.headers.get('content-type', ''):
            content = resp.text

            # 注入WebSocket重写脚本
            websocket_script = '''
<script>
(function() {
    console.log('[WebSocket Override] 开始注入WebSocket重写脚本...');

    // 存储原始 WebSocket 对象
    const OriginalWebSocket = window.WebSocket;

    // 覆盖 WebSocket 构造函数
    window.WebSocket = function(url, protocols) {
        // 替换 wss:// 为 ws://
        const modifiedUrl = url.replace(/^wss:\\/\\//i, 'ws://');
        console.log('[WebSocket Override] Original:', url, 'Modified:', modifiedUrl);

        // 使用修改后的 URL 创建 WebSocket 连接
        return new OriginalWebSocket(modifiedUrl, protocols);
    };

    // 保持原始WebSocket的属性和方法
    Object.setPrototypeOf(window.WebSocket, OriginalWebSocket);
    window.WebSocket.prototype = OriginalWebSocket.prototype;

    // 复制静态属性
    Object.getOwnPropertyNames(OriginalWebSocket).forEach(name => {
        if (name !== 'length' && name !== 'name' && name !== 'prototype') {
            try {
                window.WebSocket[name] = OriginalWebSocket[name];
            } catch (e) {
                // 忽略无法复制的属性
            }
        }
    });

    console.log('[WebSocket Override] WebSocket重写脚本注入完成');
})();
</script>
'''

            # 在</head>标签前插入脚本，如果没有</head>则在<body>前插入
            if '</head>' in content:
                content = content.replace('</head>', websocket_script + '</head>')
            elif '<body>' in content:
                content = content.replace('<body>', websocket_script + '<body>')
            else:
                # 如果都没有，就在开头插入
                content = websocket_script + content

            response.data = content.encode('utf-8')

        return response

    except requests.exceptions.RequestException as e:
        return jsonify({'error': f'代理请求失败: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'代理服务器错误: {str(e)}'}), 500

if __name__ == '__main__':
    # 启动自动检测线程
    auto_thread = threading.Thread(target=auto_detection_loop, daemon=True)
    auto_thread.start()
    
    print("三角洲地图扫描网页服务器已启动")
    print("访问 http://localhost:4000 查看监控界面")
    
    try:
        app.run(host='0.0.0.0', port=4000, debug=False)
    except KeyboardInterrupt:
        print("正在关闭服务器...")
        stop_event.set()
